"""
工具函数模块
提供通用的工具函数和辅助方法
"""

import re
import logging
from typing import Optional, Tuple, Dict, Any
from datetime import datetime, timedelta
from .constants import VERSION_PATTERNS, STATUS_COLORS, TIME_STATUS

logger = logging.getLogger(__name__)

def parse_version_environment(user_input: str) -> Tuple[Optional[str], Optional[str]]:
    """使用正则表达式解析用户输入中的版本号和环境名"""
    for pattern in VERSION_PATTERNS:
        match = re.search(pattern, user_input)
        if match:
            groups = match.groups()
            if len(groups) == 2:
                # 判断哪个是版本号，哪个是环境名
                if 'R' in groups[0]:
                    return groups[0], groups[1]
                else:
                    return groups[1], groups[0]
    return None, None

def format_time_diff(time_diff: timedelta) -> str:
    """格式化时间差为可读字符串"""
    total_seconds = int(time_diff.total_seconds())
    
    if total_seconds < 0:
        return "已过期"
    
    days = total_seconds // 86400
    hours = (total_seconds % 86400) // 3600
    minutes = (total_seconds % 3600) // 60
    
    if days > 0:
        return f"{days}天{hours}小时"
    elif hours > 0:
        return f"{hours}小时{minutes}分钟"
    else:
        return f"{minutes}分钟"

def get_status_color(status: str) -> str:
    """根据状态获取对应的颜色"""
    return STATUS_COLORS.get(status, "gray")

def safe_get(dictionary: Dict[str, Any], key: str, default: Any = None) -> Any:
    """安全地从字典中获取值"""
    try:
        return dictionary.get(key, default)
    except (AttributeError, TypeError):
        return default

def validate_job_parameters(job: Dict[str, Any]) -> bool:
    """验证Jenkins job参数是否完整"""
    required_fields = ['job_name', 'parameters']
    return all(field in job for field in required_fields)

def format_job_display_name(job: Dict[str, Any]) -> str:
    """格式化job显示名称"""
    job_name = safe_get(job, 'job_name', 'N/A')
    customer_name = safe_get(job, 'customer_name', 'N/A')
    service_name = safe_get(job, 'service_name', 'N/A')
    return f"{job_name} ({customer_name} - {service_name})"

def parse_deployment_datetime(deployment_date: str, time_window: str) -> Tuple[Optional[datetime], Optional[datetime]]:
    """解析部署日期和时间窗口"""
    try:
        if not deployment_date or not time_window:
            return None, None
        
        job_date = datetime.strptime(deployment_date, '%Y-%m-%d').date()
        
        if '-' in time_window:
            start_time_str, end_time_str = time_window.split('-')
            start_time = datetime.strptime(start_time_str.strip(), '%H:%M').time()
            end_time = datetime.strptime(end_time_str.strip(), '%H:%M').time()
            
            start_datetime = datetime.combine(job_date, start_time)
            end_datetime = datetime.combine(job_date, end_time)
            
            return start_datetime, end_datetime
        else:
            # 只有开始时间
            start_time = datetime.strptime(time_window.strip(), '%H:%M').time()
            start_datetime = datetime.combine(job_date, start_time)
            return start_datetime, None
            
    except (ValueError, IndexError) as e:
        logger.warning(f"解析部署时间失败: {e}")
        return None, None

def get_time_status(deployment_date: str, time_window: str, current_time: datetime) -> Dict[str, Any]:
    """获取时间状态信息"""
    start_datetime, end_datetime = parse_deployment_datetime(deployment_date, time_window)
    
    if not start_datetime:
        return {"status": TIME_STATUS["NO_TIME"]}
    
    if not end_datetime:
        # 只有开始时间，假设持续1小时
        end_datetime = start_datetime + timedelta(hours=1)
    
    if current_time < start_datetime:
        return {
            "status": TIME_STATUS["FUTURE"], 
            "time_until_start": format_time_diff(start_datetime - current_time)
        }
    elif current_time > end_datetime:
        return {
            "status": TIME_STATUS["PAST"], 
            "time_since_end": format_time_diff(current_time - end_datetime)
        }
    else:
        return {
            "status": TIME_STATUS["IN_TIME"], 
            "time_remaining": format_time_diff(end_datetime - current_time)
        }

def generate_release_branch(version: str) -> str:
    """生成release分支名"""
    # 25R1.2 -> release/251.2
    version_clean = version.replace('R', '')
    return f"release/{version_clean}"

def generate_release_tag(version: str) -> str:
    """生成release tag"""
    # 25R1.2 -> LR/251.2.0, 25R2.0 -> GR/252.0.0
    version_clean = version.replace('R', '')
    version_parts = version_clean.split('.')
    
    if len(version_parts) == 2:
        major = version_parts[0]
        minor = version_parts[1]
        if minor == '0':
            return f"GR/{major}.{minor}.0"
        else:
            return f"LR/{version_clean}.0"
    return f"LR/{version_clean}.0"

def truncate_text(text: str, max_length: int = 100) -> str:
    """截断文本到指定长度"""
    if len(text) <= max_length:
        return text
    return text[:max_length-3] + "..."

def is_valid_version(version: str) -> bool:
    """验证版本号格式是否正确"""
    pattern = r'^\d+R\d+\.\d+$'
    return bool(re.match(pattern, version))

def is_valid_environment(environment: str) -> bool:
    """验证环境名是否有效"""
    valid_envs = ['prod', 'production', 'staging', 'test', 'testing', 'dev', 'development']
    return environment.lower() in valid_envs

def sanitize_job_name(job_name: str) -> str:
    """清理job名称，移除特殊字符"""
    # 移除或替换可能导致问题的字符
    sanitized = re.sub(r'[^\w\-_.]', '_', job_name)
    return sanitized

def calculate_progress(current: int, total: int) -> float:
    """计算进度百分比"""
    if total == 0:
        return 0.0
    return min(current / total, 1.0)

def format_duration(seconds: int) -> str:
    """格式化持续时间"""
    if seconds < 60:
        return f"{seconds}秒"
    elif seconds < 3600:
        minutes = seconds // 60
        return f"{minutes}分钟"
    else:
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        return f"{hours}小时{minutes}分钟"
