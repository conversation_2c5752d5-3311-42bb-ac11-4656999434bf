import streamlit as st
from src.logic.llm_utils import llm_config_manager, LLMProvider

def display_sidebar(current_state):
    """显示侧边栏"""
    with st.sidebar:
        st.header("Trae AI - Release Copilot")
        st.markdown("--- RAG-Powered Release Automation ---")

        # 工作流进度
        display_workflow_progress_sidebar(current_state)

        st.divider()

        # 环境配置检查
        display_env_check_sidebar()

        st.divider()

        # LLM配置
        display_llm_config_sidebar()

        st.divider()

        # 会话重置
        if st.button("🔄 重置会话", key="reset_session_sidebar"):
            reset_session_state()
            st.rerun()

def display_workflow_progress_sidebar(current_state):
    """在侧边栏显示工作流进度"""
    st.subheader("Workflow Progress")

    # 定义所有步骤
    steps = {
        "query": "📝 Query & Approve",
        "execution_ready": "🚀 Execution Ready",
        "executing": "⚙️ Executing",
        "completed": "🎉 Completed"
    }
    step_keys = list(steps.keys())

    # 获取当前状态的索引
    try:
        current_index = step_keys.index(current_state)
    except ValueError:
        current_index = -1

    # 使用不同颜色和图标显示进度
    for i, (key, label) in enumerate(steps.items()):
        if i < current_index:
            # 已完成
            st.success(f"✅ {label}", icon="✅")
        elif i == current_index:
            # 当前
            st.info(f"➡️ {label}", icon="➡️")
        else:
            # 未开始
            st.markdown(f"<span style='color: #888;'>⚪ {label}</span>", unsafe_allow_html=True)

def display_env_check_sidebar():
    """在侧边栏显示环境配置检查"""
    with st.expander("🛠️ Environment Config Check", expanded=False):
        st.info("Checking API keys in .env file...")
        # 检查每个LLM提供商的API密钥
        for provider in LLMProvider:
            api_key = llm_config_manager.get_api_key(provider)
            provider_name = llm_config_manager.get_provider_display_name(provider)
            if api_key:
                st.success(f"✅ {provider_name} API Key found.")
            else:
                st.error(f"❌ {provider_name} API Key not found.")

def display_llm_config_sidebar():
    """在侧边栏显示LLM配置"""
    st.subheader("🤖 LLM Configuration")

    # 获取当前配置
    current_config = llm_config_manager.get_current_config()
    st.info(f"Provider: **{current_config['provider_name']}**")
    st.info(f"Model: **{current_config['model']}**")

    # 高级配置按钮
    if st.button("⚙️ Advanced LLM Config", key="advanced_config_sidebar"):
        st.session_state.show_advanced_llm_config = True
        st.rerun()

def reset_session_state():
    """重置Streamlit会话状态"""
    # 保留必要的初始状态
    keys_to_keep = ['show_advanced_llm_config']
    preserved_state = {k: st.session_state[k] for k in keys_to_keep if k in st.session_state}

    # 清空会话状态
    st.session_state.clear()

    # 恢复保留的状态
    for k, v in preserved_state.items():
        st.session_state[k] = v

    # 重新初始化应用状态
    from app import initialize_app_state
    initialize_app_state()