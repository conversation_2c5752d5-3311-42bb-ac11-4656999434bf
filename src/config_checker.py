"""
配置检查模块
检查数据库、Jenkins和LLM的配置状态
"""

import os
import logging
from typing import Dict, Any, Optional
from dotenv import load_dotenv

# 确保加载环境变量
load_dotenv()

logger = logging.getLogger(__name__)

class ConfigChecker:
    """配置状态检查器"""
    
    def __init__(self):
        self.config_status = {}
        self.refresh_status()
    
    def refresh_status(self):
        """刷新所有配置状态"""
        self.config_status = {
            "database": self.check_database_config(),
            "jenkins": self.check_jenkins_config(),
            "llm": self.check_llm_config()
        }
    
    def check_database_config(self) -> Dict[str, Any]:
        """检查数据库配置"""
        config = {
            "name": "数据库配置",
            "icon": "🗄️",
            "status": "unknown",
            "details": {},
            "connection_test": None
        }
        
        # 检查必需的环境变量
        required_vars = ['MYSQL_HOST', 'MYSQL_PORT', 'MYSQL_USER', 'MYSQL_PASSWORD', 'MYSQL_DATABASE']
        missing_vars = []
        
        for var in required_vars:
            value = os.getenv(var)
            if value:
                config["details"][var] = {
                    "status": "✅ 已配置",
                    "value": value if var != 'MYSQL_PASSWORD' else "***"
                }
            else:
                config["details"][var] = {
                    "status": "❌ 未配置",
                    "value": None
                }
                missing_vars.append(var)
        
        if missing_vars:
            config["status"] = "error"
            config["message"] = f"缺少配置: {', '.join(missing_vars)}"
        else:
            config["status"] = "configured"
            config["message"] = "配置完整"
            # 尝试测试连接
            config["connection_test"] = self._test_database_connection()
        
        return config
    
    def check_jenkins_config(self) -> Dict[str, Any]:
        """检查Jenkins配置"""
        config = {
            "name": "Jenkins配置",
            "icon": "🔧",
            "status": "unknown",
            "details": {},
            "connection_test": None
        }
        
        # 检查必需的环境变量
        required_vars = ['JENKINS_URL', 'JENKINS_USERNAME', 'JENKINS_TOKEN']
        missing_vars = []
        
        for var in required_vars:
            value = os.getenv(var)
            if value:
                config["details"][var] = {
                    "status": "✅ 已配置",
                    "value": value if var != 'JENKINS_TOKEN' else "***"
                }
            else:
                config["details"][var] = {
                    "status": "❌ 未配置",
                    "value": None
                }
                missing_vars.append(var)
        
        if missing_vars:
            config["status"] = "error"
            config["message"] = f"缺少配置: {', '.join(missing_vars)}"
        else:
            config["status"] = "configured"
            config["message"] = "配置完整"
            # 尝试测试连接
            config["connection_test"] = self._test_jenkins_connection()
        
        return config
    
    def check_llm_config(self) -> Dict[str, Any]:
        """检查LLM配置"""
        config = {
            "name": "LLM配置",
            "icon": "🤖",
            "status": "unknown",
            "details": {},
            "providers": {}
        }
        
        # 检查各个LLM供应商的API密钥
        providers = {
            "OpenAI": "OPENAI_API_KEY",
            "Google": "GOOGLE_API_KEY", 
            "Anthropic": "ANTHROPIC_API_KEY"
        }
        
        configured_providers = []
        
        for provider_name, env_var in providers.items():
            api_key = os.getenv(env_var)
            if api_key and len(api_key) > 10 and not api_key.startswith('your_'):
                config["providers"][provider_name] = {
                    "status": "✅ 已配置",
                    "key_preview": f"{api_key[:8]}..."
                }
                configured_providers.append(provider_name)
            else:
                config["providers"][provider_name] = {
                    "status": "❌ 未配置",
                    "key_preview": None
                }
        
        # 检查默认配置
        default_provider = os.getenv('DEFAULT_LLM_PROVIDER', 'openai')
        default_model = os.getenv('DEFAULT_LLM_MODEL', 'gpt-3.5-turbo')
        
        config["details"]["DEFAULT_LLM_PROVIDER"] = {
            "status": "✅ 已设置",
            "value": default_provider
        }
        config["details"]["DEFAULT_LLM_MODEL"] = {
            "status": "✅ 已设置", 
            "value": default_model
        }
        
        if configured_providers:
            config["status"] = "configured"
            config["message"] = f"已配置 {len(configured_providers)} 个供应商"
        else:
            config["status"] = "warning"
            config["message"] = "未配置任何LLM供应商"
        
        return config
    
    def _test_database_connection(self) -> Dict[str, Any]:
        """测试数据库连接"""
        try:
            import mysql.connector

            connection = mysql.connector.connect(
                host=os.getenv('MYSQL_HOST'),
                port=int(os.getenv('MYSQL_PORT', 3306)),
                user=os.getenv('MYSQL_USER'),
                password=os.getenv('MYSQL_PASSWORD'),
                database=os.getenv('MYSQL_DATABASE'),
                charset='utf8mb4',
                connect_timeout=5  # 5秒超时
            )

            if connection.is_connected():
                # 测试查询
                cursor = connection.cursor()
                cursor.execute("SELECT 1")
                cursor.fetchone()
                cursor.close()
                connection.close()

                return {
                    "status": "success",
                    "message": "✅ 连接成功，数据库可访问"
                }
            else:
                return {
                    "status": "error",
                    "message": "❌ 连接失败"
                }

        except mysql.connector.Error as e:
            return {
                "status": "error",
                "message": f"❌ 数据库错误: {e.msg}"
            }
        except Exception as e:
            return {
                "status": "error",
                "message": f"❌ 连接失败: {str(e)}"
            }
    
    def _test_jenkins_connection(self) -> Dict[str, Any]:
        """测试Jenkins连接"""
        try:
            import jenkins
            
            server = jenkins.Jenkins(
                url=os.getenv('JENKINS_URL'),
                username=os.getenv('JENKINS_USERNAME'),
                password=os.getenv('JENKINS_TOKEN')
            )
            
            # 尝试获取用户信息
            user = server.get_whoami()
            return {
                "status": "success",
                "message": f"✅ 连接成功 (用户: {user.get('fullName', 'Unknown')})"
            }
            
        except Exception as e:
            return {
                "status": "error",
                "message": f"❌ 连接失败: {str(e)}"
            }
    
    def get_overall_status(self) -> str:
        """获取整体配置状态"""
        statuses = [config["status"] for config in self.config_status.values()]
        
        if all(status == "configured" for status in statuses):
            return "all_good"
        elif any(status == "error" for status in statuses):
            return "has_errors"
        else:
            return "has_warnings"
    
    def get_status_summary(self) -> Dict[str, int]:
        """获取状态摘要"""
        summary = {"configured": 0, "error": 0, "warning": 0}
        
        for config in self.config_status.values():
            status = config["status"]
            if status == "configured":
                summary["configured"] += 1
            elif status == "error":
                summary["error"] += 1
            else:
                summary["warning"] += 1
        
        return summary

# 全局配置检查器实例
config_checker = ConfigChecker()
