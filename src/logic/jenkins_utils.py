"""
This module contains utility functions for interacting with the Jenkins API.
"""

import streamlit as st
import jenkins
from functools import lru_cache
from loguru import logger

@st.cache_resource
def get_jenkins_server():
    """Connect to <PERSON> server."""
    try:
        server = jenkins.Jenkins(st.secrets["JENKINS_URL"], username=st.secrets["JENKINS_USERNAME"], password=st.secrets["JENKINS_PASSWORD"])
        server.get_whoami()
        return server
    except Exception as e:
        logger.error(f"Failed to connect to <PERSON>: {e}")
        return None

@st.cache_data(ttl=300)  # Cache for 5 minutes
def get_all_jenkins_jobs():
    """Get all Jenkins jobs."""
    server = get_jenkins_server()
    if server:
        try:
            return server.get_jobs()
        except Exception as e:
            logger.error(f"Failed to get Jenkins jobs: {e}")
    return []

@st.cache_data(ttl=60)
def get_job_details(job_name):
    server = get_jenkins_server()
    if server and job_name:
        try:
            return server.get_job_info(job_name)
        except Exception as e:
            logger.error(f"Failed to get job details for {job_name}: {e}")
    return None

def trigger_jenkins_job(job_name, params):
    server = get_jenkins_server()
    if server:
        try:
            queue_item = server.build_job(job_name, parameters=params)
            return queue_item
        except Exception as e:
            logger.error(f"Failed to trigger job {job_name}: {e}")
    return None

@st.cache_data(ttl=10)
def get_build_info(job_name, build_number):
    server = get_jenkins_server()
    if server:
        try:
            return server.get_build_info(job_name, build_number)
        except Exception as e:
            logger.error(f"Failed to get build info for {job_name} #{build_number}: {e}")
    return None

@st.cache_data(ttl=10)
def get_build_console_output(job_name, build_number):
    server = get_jenkins_server()
    if server:
        try:
            return server.get_build_console_output(job_name, build_number)
        except Exception as e:
            logger.error(f"Failed to get console output for {job_name} #{build_number}: {e}")
    return ""