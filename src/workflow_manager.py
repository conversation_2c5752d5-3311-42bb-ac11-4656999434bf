"""
工作流状态管理器
管理和跟踪工作流的执行状态
"""

import streamlit as st
from typing import Dict, Any, List, Optional
from datetime import datetime

class WorkflowManager:
    """工作流状态管理器"""
    
    # 定义工作流步骤
    WORKFLOW_STEPS = [
        {
            "key": "plan_query",
            "name": "计划查询",
            "icon": "📝",
            "description": "查询和生成发布计划"
        },
        {
            "key": "plan_approval",
            "name": "计划审批",
            "icon": "✅",
            "description": "审核和确认发布计划"
        },
        {
            "key": "execution",
            "name": "执行管理",
            "icon": "🚀",
            "description": "选择要执行的Jenkins Jobs"
        },
        {
            "key": "execution_confirmation",
            "name": "执行确认",
            "icon": "🔍",
            "description": "确认Job参数和执行顺序"
        },
        {
            "key": "monitoring",
            "name": "状态监控",
            "icon": "📊",
            "description": "监控执行状态和结果"
        }
    ]
    
    @classmethod
    def get_current_step(cls) -> str:
        """获取当前工作流步骤"""
        # 检查是否在执行确认阶段
        if st.session_state.get('show_execution_confirmation'):
            return "execution_confirmation"

        # 检查是否在监控阶段
        if 'execution_status' in st.session_state:
            # 检查是否所有Jobs都已完成
            execution_status = st.session_state.execution_status
            if all(status.get('status') in ['SUCCESS', 'FAILURE', 'ABORTED']
                   for status in execution_status.values()):
                return "completed"
            return "monitoring"

        # 从会话状态判断当前步骤
        if 'deployment_plan' not in st.session_state or not st.session_state.deployment_plan:
            return "plan_query"

        deployment_plan = st.session_state.deployment_plan
        workflow_state = deployment_plan.get("workflow_state", "plan_generated")

        # 根据工作流状态映射到步骤
        state_mapping = {
            "start": "plan_query",
            "plan_generated": "plan_approval",
            "plan_approved": "execution",
            "execution_ready": "execution",
            "executing": "monitoring",
            "monitoring": "monitoring",
            "completed": "monitoring"
        }

        return state_mapping.get(workflow_state, "plan_query")
    
    @classmethod
    def get_step_index(cls, step_key: str) -> int:
        """获取步骤索引"""
        for i, step in enumerate(cls.WORKFLOW_STEPS):
            if step["key"] == step_key:
                return i
        return 0
    
    @classmethod
    def get_step_info(cls, step_key: str) -> Dict[str, Any]:
        """获取步骤信息"""
        for step in cls.WORKFLOW_STEPS:
            if step["key"] == step_key:
                return step
        return cls.WORKFLOW_STEPS[0]
    
    @classmethod
    def get_progress_percentage(cls, step_key: str) -> float:
        """获取进度百分比"""
        step_index = cls.get_step_index(step_key)
        return (step_index + 1) / len(cls.WORKFLOW_STEPS)
    
    @classmethod
    def is_step_completed(cls, step_key: str, current_step: str) -> bool:
        """判断步骤是否已完成"""
        # 特殊处理：如果当前在监控阶段或已完成，执行确认步骤应该显示为已完成
        if current_step in ["monitoring", "completed"] and step_key == "execution_confirmation":
            # 检查是否曾经进入过执行确认阶段（即有选中的Jobs）
            return 'selected_jobs_for_execution' in st.session_state or 'execution_status' in st.session_state

        # 特殊处理：如果工作流已完成，监控步骤也应该显示为完成
        if current_step == "completed" and step_key == "monitoring":
            return True

        step_index = cls.get_step_index(step_key)
        current_index = cls.get_step_index(current_step)
        return step_index < current_index
    
    @classmethod
    def is_step_current(cls, step_key: str, current_step: str) -> bool:
        """判断是否为当前步骤"""
        return step_key == current_step
    
    @classmethod
    def get_execution_status_summary(cls) -> Optional[Dict[str, Any]]:
        """获取执行状态摘要"""
        if 'execution_status' not in st.session_state or not st.session_state.execution_status:
            return None
        
        execution_status = st.session_state.execution_status
        
        total_jobs = len(execution_status)
        if total_jobs == 0:
            return None
        
        # 统计各种状态的job数量
        status_counts = {
            "SUCCESS": 0,
            "RUNNING": 0, 
            "FAILURE": 0,
            "ABORTED": 0,
            "PENDING": 0
        }
        
        for job_status in execution_status.values():
            status = job_status.get('status', 'PENDING')
            if status in status_counts:
                status_counts[status] += 1
            else:
                status_counts["PENDING"] += 1
        
        completed_jobs = status_counts["SUCCESS"] + status_counts["FAILURE"] + status_counts["ABORTED"]
        
        return {
            "total_jobs": total_jobs,
            "completed_jobs": completed_jobs,
            "success_count": status_counts["SUCCESS"],
            "running_count": status_counts["RUNNING"],
            "failure_count": status_counts["FAILURE"] + status_counts["ABORTED"],
            "pending_count": status_counts["PENDING"],
            "completion_percentage": completed_jobs / total_jobs if total_jobs > 0 else 0
        }
    
    @classmethod
    def update_workflow_step(cls, step_key: str):
        """更新工作流步骤"""
        st.session_state.workflow_step = step_key
        
        # 记录步骤变更时间
        if 'workflow_history' not in st.session_state:
            st.session_state.workflow_history = []
        
        st.session_state.workflow_history.append({
            "step": step_key,
            "timestamp": datetime.now(),
            "step_name": cls.get_step_info(step_key)["name"]
        })
    
    @classmethod
    def get_workflow_history(cls) -> List[Dict[str, Any]]:
        """获取工作流历史"""
        return st.session_state.get('workflow_history', [])
    
    @classmethod
    def reset_workflow(cls):
        """重置工作流状态"""
        # 清除工作流相关的会话状态
        workflow_keys = [
            'workflow_step',
            'workflow_history', 
            'deployment_plan',
            'execution_status',
            'raw_jenkins_jobs',
            'confirming_execution',
            'jobs_to_execute'
        ]
        
        for key in workflow_keys:
            if key in st.session_state:
                del st.session_state[key]
    
    @classmethod
    def get_next_step_suggestion(cls, current_step: str) -> Optional[str]:
        """获取下一步建议"""
        current_index = cls.get_step_index(current_step)
        
        if current_index < len(cls.WORKFLOW_STEPS) - 1:
            next_step = cls.WORKFLOW_STEPS[current_index + 1]
            return f"下一步: {next_step['icon']} {next_step['name']}"
        else:
            return "工作流已完成"
    
    @classmethod
    def can_proceed_to_next_step(cls, current_step: str) -> bool:
        """判断是否可以进入下一步"""
        if current_step == "plan_query":
            # 需要有部署计划
            return 'deployment_plan' in st.session_state and st.session_state.deployment_plan
        elif current_step == "plan_approval":
            # 需要计划已审批
            if 'deployment_plan' in st.session_state:
                workflow_state = st.session_state.deployment_plan.get("workflow_state", "")
                return workflow_state == "plan_approved"
            return False
        elif current_step == "execution":
            # 需要有执行状态
            return 'execution_status' in st.session_state and st.session_state.execution_status
        else:
            return True

# 全局工作流管理器实例
workflow_manager = WorkflowManager()
