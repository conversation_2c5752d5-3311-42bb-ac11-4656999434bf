"""
配置状态显示组件
提供美观的配置状态显示界面
"""

import streamlit as st
from src.config_checker import config_checker

def display_config_status_card(config_name: str, config_data: dict):
    """显示配置状态卡片"""
    status = config_data["status"]
    icon = config_data["icon"]
    name = config_data["name"]
    message = config_data["message"]
    
    # 根据状态选择颜色
    if status == "configured":
        status_color = "🟢"
        border_color = "#28a745"
    elif status == "warning":
        status_color = "🟡"
        border_color = "#ffc107"
    else:
        status_color = "🔴"
        border_color = "#dc3545"
    
    # 创建状态卡片
    st.markdown(f"""
    <div style="
        border: 2px solid {border_color};
        border-radius: 10px;
        padding: 15px;
        margin: 10px 0;
        background-color: rgba(255, 255, 255, 0.05);
    ">
        <h4 style="margin: 0; color: {border_color};">
            {icon} {name} {status_color}
        </h4>
        <p style="margin: 5px 0; color: #666;">
            {message}
        </p>
    </div>
    """, unsafe_allow_html=True)

def display_connection_test_result(test_result: dict):
    """显示连接测试结果"""
    if not test_result:
        return
    
    if test_result["status"] == "success":
        st.success(test_result["message"])
    else:
        st.error(test_result["message"])

def display_config_details_table(details: dict):
    """显示配置详情表格"""
    if not details:
        return
    
    # 创建表格数据
    table_data = []
    for key, info in details.items():
        table_data.append({
            "配置项": key,
            "状态": info["status"],
            "值": info["value"] if info["value"] else "未设置"
        })
    
    # 显示表格
    st.table(table_data)

def display_provider_status(providers: dict):
    """显示LLM供应商状态"""
    if not providers:
        return
    
    st.write("**供应商状态:**")
    
    cols = st.columns(len(providers))
    for i, (provider, info) in enumerate(providers.items()):
        with cols[i]:
            if info["key_preview"]:
                st.success(f"✅ {provider}")
                st.caption(f"密钥: {info['key_preview']}")
            else:
                st.error(f"❌ {provider}")
                st.caption("未配置")

def display_overall_status():
    """显示总体配置状态"""
    # 刷新配置状态
    config_checker.refresh_status()
    
    # 获取状态摘要
    summary = config_checker.get_status_summary()
    overall_status = config_checker.get_overall_status()
    
    # 显示总体状态标题
    if overall_status == "all_good":
        st.success("🎉 所有配置正常")
    elif overall_status == "has_errors":
        st.error("⚠️ 存在配置错误")
    else:
        st.warning("⚠️ 存在配置警告")
    
    # 显示状态统计
    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric(
            label="✅ 正常",
            value=summary["configured"],
            delta=None
        )
    with col2:
        st.metric(
            label="⚠️ 警告", 
            value=summary["warning"],
            delta=None
        )
    with col3:
        st.metric(
            label="❌ 错误",
            value=summary["error"], 
            delta=None
        )
    
    return overall_status, summary

def display_database_status():
    """显示数据库状态"""
    db_config = config_checker.config_status["database"]
    
    st.subheader("🗄️ 数据库配置")
    
    # 显示状态卡片
    display_config_status_card("database", db_config)
    
    # 显示配置详情
    if st.checkbox("显示详细配置", key="db_details"):
        display_config_details_table(db_config["details"])
    
    # 显示连接测试结果
    display_connection_test_result(db_config.get("connection_test"))

def display_jenkins_status():
    """显示Jenkins状态"""
    jenkins_config = config_checker.config_status["jenkins"]
    
    st.subheader("🔧 Jenkins配置")
    
    # 显示状态卡片
    display_config_status_card("jenkins", jenkins_config)
    
    # 显示配置详情
    if st.checkbox("显示详细配置", key="jenkins_details"):
        display_config_details_table(jenkins_config["details"])
    
    # 显示连接测试结果
    display_connection_test_result(jenkins_config.get("connection_test"))

def display_llm_status():
    """显示LLM状态"""
    llm_config = config_checker.config_status["llm"]
    
    st.subheader("🤖 LLM配置")
    
    # 显示状态卡片
    display_config_status_card("llm", llm_config)
    
    # 显示供应商状态
    display_provider_status(llm_config["providers"])
    
    # 显示默认配置
    if st.checkbox("显示默认配置", key="llm_details"):
        display_config_details_table(llm_config["details"])

def display_full_config_status():
    """显示完整的配置状态页面"""
    st.title("⚙️ 系统配置状态")
    st.markdown("---")
    
    # 刷新按钮
    col1, col2 = st.columns([1, 4])
    with col1:
        if st.button("🔄 刷新状态", use_container_width=True):
            config_checker.refresh_status()
            st.rerun()
    
    # 显示总体状态
    overall_status, summary = display_overall_status()
    
    st.markdown("---")
    
    # 显示各个配置状态
    tab1, tab2, tab3 = st.tabs(["🗄️ 数据库", "🔧 Jenkins", "🤖 LLM"])
    
    with tab1:
        display_database_status()
    
    with tab2:
        display_jenkins_status()
    
    with tab3:
        display_llm_status()
    
    # 显示配置建议
    st.markdown("---")
    st.subheader("💡 配置建议")
    
    if overall_status == "all_good":
        st.success("🎉 所有配置都正常！系统已准备就绪。")
    else:
        if summary["error"] > 0:
            st.error("❌ 请检查并修复配置错误，确保系统正常运行。")
        if summary["warning"] > 0:
            st.warning("⚠️ 建议完善配置以获得最佳体验。")
        
        st.info("""
        **配置文件位置:** `.env`
        
        **必需配置项:**
        - 数据库: MYSQL_HOST, MYSQL_PORT, MYSQL_USER, MYSQL_PASSWORD, MYSQL_DATABASE
        - Jenkins: JENKINS_URL, JENKINS_USERNAME, JENKINS_TOKEN
        - LLM: 至少配置一个供应商的API密钥 (GOOGLE_API_KEY, OPENAI_API_KEY, 或 ANTHROPIC_API_KEY)
        """)
