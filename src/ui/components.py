import streamlit as st
from datetime import datetime

def parse_deployment_plan_table(deployment_plan_text: str):
    """解析发布计划表格文本"""
    lines = deployment_plan_text.strip().split('\n')
    plan_items = []

    # 找到表格开始位置
    table_start = -1
    for i, line in enumerate(lines):
        if '| 计划部署日期 |' in line:
            table_start = i + 2  # 跳过表头和分隔线
            break

    if table_start == -1:
        return []

    # 解析表格行
    for line in lines[table_start:]:
        line = line.strip()
        if not line or not line.startswith('|'):
            continue

        # 分割表格列
        columns = [col.strip() for col in line.split('|')[1:-1]]

        if len(columns) >= 6:
            plan_items.append({
                '计划部署日期': columns[0],
                '时间窗口': columns[1],
                '客户名': columns[2],
                '租户名': columns[3],
                'Service名': columns[4],
                '是否部署PS代码': columns[5]
            })

    return plan_items

def parse_task_datetime(item: dict):
    """解析任务的日期时间"""
    try:
        date_str = item.get('计划部署日期', '').strip()
        time_str = item.get('时间窗口', '').strip()

        if not date_str or not time_str:
            return None

        date_obj = datetime.strptime(date_str, '%Y-%m-%d').date()

        if '-' in time_str:
            start_time_str = time_str.split('-')[0].strip()
        else:
            start_time_str = time_str

        time_obj = datetime.strptime(start_time_str, '%H:%M').time()

        return datetime.combine(date_obj, time_obj)

    except (ValueError, IndexError):
        return None

def analyze_job_time_windows(jobs: list, current_time: datetime):
    """分析所有jobs的时间窗口状态"""
    analysis = {
        "deployable_jobs": [],
        "future_jobs": [],
        "expired_jobs": [],
        "no_time_jobs": []
    }

    for i, job in enumerate(jobs):
        deployment_date = job.get('deployment_date', '')
        time_window = job.get('time_window', '')

        time_status = parse_job_time_status(deployment_date, time_window, current_time)

        job_info = {
            "job": job,
            "index": i,
            "time_status": time_status
        }

        if time_status["status"] == "in_time":
            analysis["deployable_jobs"].append(job_info)
        elif time_status["status"] == "future":
            analysis["future_jobs"].append(job_info)
        elif time_status["status"] == "past":
            analysis["expired_jobs"].append(job_info)
        else:  # no_time
            analysis["no_time_jobs"].append(job_info)

    return analysis

def parse_job_time_status(deployment_date: str, time_window: str, current_time: datetime):
    """解析job的时间状态"""
    try:
        if not deployment_date or not time_window:
            return {"status": "no_time"}

        job_date = datetime.strptime(deployment_date, '%Y-%m-%d').date()
        start_time_str, end_time_str = time_window.split('-')
        start_time = datetime.strptime(start_time_str.strip(), '%H:%M').time()
        end_time = datetime.strptime(end_time_str.strip(), '%H:%M').time()

        start_datetime = datetime.combine(job_date, start_time)
        end_datetime = datetime.combine(job_date, end_time)

        if current_time < start_datetime:
            return {"status": "future", "time_until_start": format_time_diff(start_datetime - current_time)}
        elif current_time > end_datetime:
            return {"status": "past", "time_since_end": format_time_diff(current_time - end_datetime)}
        else:
            return {"status": "in_time", "time_remaining": format_time_diff(end_datetime - current_time)}

    except (ValueError, IndexError):
        return {"status": "no_time"}

def format_time_diff(time_diff):
    """格式化时间差"""
    total_seconds = int(time_diff.total_seconds())

    if total_seconds < 0:
        return "已过期"

    days = total_seconds // 86400
    hours = (total_seconds % 86400) // 3600
    minutes = (total_seconds % 3600) // 60

    if days > 0:
        return f"{days}天{hours}小时"
    elif hours > 0:
        return f"{hours}小时{minutes}分钟"
    else:
        return f"{minutes}分钟"

def display_time_analysis_summary_enhanced(time_analysis: dict, current_time: datetime):
    """增强版时间分析摘要显示"""
    st.subheader("⏰ 时间窗口分析")

    st.markdown(f"""
    <div style="padding: 10px; background-color: #f0f2f6; border-radius: 5px; margin-bottom: 15px;">
        <strong>📅 当前系统时间:</strong> {current_time.strftime('%Y-%m-%d %H:%M:%S')}
    </div>
    """, unsafe_allow_html=True)

    col1, col2, col3, col4 = st.columns(4)

    deployable_count = len(time_analysis["deployable_jobs"])
    future_count = len(time_analysis["future_jobs"])
    expired_count = len(time_analysis["expired_jobs"])

    col1.metric("🟢 可部署", f"{deployable_count} 个")
    col2.metric("🟡 未到时间", f"{future_count} 个")
    col3.metric("🔴 已过期", f"{expired_count} 个")
    col4.metric("⚪ 无时间限制", f"{len(time_analysis['no_time_jobs'])} 个")

    if deployable_count > 0:
        st.success(f"✅ **推荐执行**: 当前有 {deployable_count} 个Jobs符合部署时间条件，已自动勾选。")
    if future_count > 0:
        st.info(f"ℹ️ 有 {future_count} 个Jobs将在未来执行。")
    if expired_count > 0:
        st.error(f"⚠️ **注意**: 有 {expired_count} 个Jobs已错过部署时间窗口。")

def add_test_time_data(jobs: list):
    """为演示目的，为jobs添加测试用的时间数据"""
    from datetime import timedelta

    now = datetime.now()
    for i, job in enumerate(jobs):
        if i % 4 == 0:
            # 可部署
            job['deployment_date'] = now.strftime('%Y-%m-%d')
            start = now - timedelta(minutes=30)
            end = now + timedelta(minutes=30)
            job['time_window'] = f"{start.strftime('%H:%M')}-{end.strftime('%H:%M')}"
        elif i % 4 == 1:
            # 未来
            start = now + timedelta(hours=1)
            end = now + timedelta(hours=2)
            job['deployment_date'] = start.strftime('%Y-%m-%d')
            job['time_window'] = f"{start.strftime('%H:%M')}-{end.strftime('%H:%M')}"
        elif i % 4 == 2:
            # 已过期
            start = now - timedelta(hours=2)
            end = now - timedelta(hours=1)
            job['deployment_date'] = start.strftime('%Y-%m-%d')
            job['time_window'] = f"{start.strftime('%H:%M')}-{end.strftime('%H:%M')}"
        else:
            # 无时间
            job['deployment_date'] = ''
            job['time_window'] = ''
    return jobs