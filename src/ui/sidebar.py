import streamlit as st
from src.logic.llm_utils import llm_config_manager, LLMProvider
from src.config_checker import config_checker
from src.workflow_manager import workflow_manager

def display_sidebar():
    """显示侧边栏"""
    with st.sidebar:
        st.image("https://www.inf-it.com/wp-content/uploads/2020/08/cropped-logo_inf-it_header.png", width=200)
        st.title("Release Helper")
        st.caption("v2.0 Modern")

        # 工作流进度 - 突出显示，不折叠
        st.markdown("### 📊 工作流进度")
        display_workflow_progress_sidebar()

        st.divider()

        # 配置状态总览
        display_config_overview()

        # 数据库配置
        with st.expander("🗄️ 数据库配置"):
            display_database_config()

        # Jenkins配置
        with st.expander("🔧 Jenkins配置"):
            display_jenkins_config()

        # LLM 配置 - 默认折叠
        with st.expander("🤖 LLM 配置", expanded=False):
            display_llm_config_detailed()

        st.divider()

        # 重置会话
        if st.button("🔄 重置会话状态", use_container_width=True):
            reset_session_state()
            st.success("会话状态已重置")
            st.rerun()

def display_config_overview():
    """显示配置状态总览"""
    # 刷新配置状态
    if st.button("🔄 刷新配置状态", use_container_width=True):
        config_checker.refresh_status()
        st.rerun()

    # 获取状态摘要
    summary = config_checker.get_status_summary()
    overall_status = config_checker.get_overall_status()

    # 显示总体状态
    if overall_status == "all_good":
        st.success("🎉 所有配置正常")
    elif overall_status == "has_errors":
        st.error("⚠️ 存在配置错误")
    else:
        st.warning("⚠️ 存在配置警告")

    # 显示状态统计
    col1, col2, col3 = st.columns(3)
    col1.metric("✅ 正常", summary["configured"])
    col2.metric("⚠️ 警告", summary["warning"])
    col3.metric("❌ 错误", summary["error"])

def display_database_config():
    """显示数据库配置详情"""
    db_config = config_checker.config_status["database"]

    # 显示状态
    if db_config["status"] == "configured":
        st.success(f"✅ {db_config['message']}")
    else:
        st.error(f"❌ {db_config['message']}")

    # 显示配置详情
    for var, info in db_config["details"].items():
        col1, col2 = st.columns([1, 2])
        col1.write(f"**{var}:**")
        col2.write(f"{info['status']} `{info['value']}`")

    # 显示连接测试结果
    if db_config["connection_test"]:
        test_result = db_config["connection_test"]
        if test_result["status"] == "success":
            st.success(test_result["message"])
        else:
            st.error(test_result["message"])

def display_jenkins_config():
    """显示Jenkins配置详情"""
    jenkins_config = config_checker.config_status["jenkins"]

    # 显示状态
    if jenkins_config["status"] == "configured":
        st.success(f"✅ {jenkins_config['message']}")
    else:
        st.error(f"❌ {jenkins_config['message']}")

    # 显示配置详情
    for var, info in jenkins_config["details"].items():
        col1, col2 = st.columns([1, 2])
        col1.write(f"**{var}:**")
        col2.write(f"{info['status']} `{info['value']}`")

    # 显示连接测试结果
    if jenkins_config["connection_test"]:
        test_result = jenkins_config["connection_test"]
        if test_result["status"] == "success":
            st.success(test_result["message"])
        else:
            st.error(test_result["message"])

def display_llm_config_detailed():
    """显示详细的LLM配置"""
    llm_config = config_checker.config_status["llm"]

    # 显示状态
    if llm_config["status"] == "configured":
        st.success(f"✅ {llm_config['message']}")
    elif llm_config["status"] == "warning":
        st.warning(f"⚠️ {llm_config['message']}")
    else:
        st.error(f"❌ {llm_config['message']}")

    # 显示各供应商状态
    st.write("**供应商状态:**")
    for provider, info in llm_config["providers"].items():
        col1, col2 = st.columns([1, 2])
        col1.write(f"**{provider}:**")
        if info["key_preview"]:
            col2.write(f"{info['status']} `{info['key_preview']}`")
        else:
            col2.write(info['status'])

    # 显示默认配置
    st.write("**默认配置:**")
    for var, info in llm_config["details"].items():
        col1, col2 = st.columns([1, 2])
        col1.write(f"**{var.replace('DEFAULT_LLM_', '')}:**")
        col2.write(f"{info['status']} `{info['value']}`")

    st.divider()

    # 原有的LLM配置选择器
    st.write("**配置选择:**")
    providers = llm_config_manager.get_available_providers()

    # 获取当前配置
    current_config = llm_config_manager.get_current_config()
    current_provider_value = current_config['provider']
    current_model_value = current_config['model']

    # 获取当前provider在列表中的索引
    provider_values = [p['value'] for p in providers]
    try:
        current_provider_index = provider_values.index(current_provider_value)
    except ValueError:
        current_provider_index = 0

    selected_provider = st.selectbox(
        "选择LLM供应商",
        options=providers,
        index=current_provider_index,
        format_func=lambda p: f"{p['label']} {p['status']}"
    )

    if selected_provider:
        provider_enum = LLMProvider(selected_provider['value'])
        models = llm_config_manager.get_available_models(provider_enum)
        model_names = [m['value'] for m in models]

        try:
            current_model_index = model_names.index(current_model_value)
        except ValueError:
            current_model_index = 0

        if model_names:
            selected_model = st.selectbox(
                "选择模型",
                options=model_names,
                index=current_model_index,
                format_func=lambda m: llm_config_manager._format_model_name(m)
            )

            if st.button("应用配置", use_container_width=True):
                llm_config_manager.set_current_config(selected_provider['value'], selected_model)
                st.success(f"LLM配置已更新为: {selected_provider['label']} - {selected_model}")
        else:
            st.warning("该供应商没有可用的模型")

def display_workflow_progress_sidebar():
    """显示工作流进度"""
    # 获取当前步骤和步骤信息
    current_step = workflow_manager.get_current_step()
    current_step_info = workflow_manager.get_step_info(current_step)
    current_index = workflow_manager.get_step_index(current_step)
    progress_percent = workflow_manager.get_progress_percentage(current_step)

    # 显示当前阶段 - 更加突出的样式
    st.markdown(f"""
    <div style="
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 20px;
        border-radius: 15px;
        text-align: center;
        margin-bottom: 20px;
        color: white;
        font-weight: bold;
        box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        border: 2px solid rgba(255,255,255,0.1);
    ">
        <h2 style="margin: 0; color: white; font-size: 1.2em;">
            {current_step_info['icon']} {current_step_info['name']}
        </h2>
        <p style="margin: 8px 0 0 0; opacity: 0.9; font-size: 0.9em;">
            第 {current_index + 1} 步 / 共 {len(workflow_manager.WORKFLOW_STEPS)} 步
        </p>
        <p style="margin: 5px 0 0 0; opacity: 0.8; font-size: 0.8em;">
            {current_step_info['description']}
        </p>
    </div>
    """, unsafe_allow_html=True)

    # 显示整体进度条
    st.progress(progress_percent, text=f"整体进度: {int(progress_percent * 100)}%")

    # 显示步骤列表
    st.markdown("**📋 流程步骤:**")
    for step in workflow_manager.WORKFLOW_STEPS:
        if workflow_manager.is_step_completed(step["key"], current_step):
            # 已完成的步骤
            st.markdown(f"✅ {step['icon']} ~~{step['name']}~~")
        elif workflow_manager.is_step_current(step["key"], current_step):
            # 当前步骤
            st.markdown(f"🔄 {step['icon']} **{step['name']}** ← 当前")
        else:
            # 未开始的步骤
            st.markdown(f"⏳ {step['icon']} {step['name']}")

    # 显示执行状态（如果有）
    execution_summary = workflow_manager.get_execution_status_summary()
    if execution_summary:
        st.markdown("---")
        st.markdown("**🚀 执行状态:**")

        # 执行进度条
        exec_progress = execution_summary["completion_percentage"]
        st.progress(exec_progress, text=f"Jobs: {execution_summary['completed_jobs']}/{execution_summary['total_jobs']}")

        # 状态统计 - 使用更紧凑的布局
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("✅", execution_summary["success_count"], label_visibility="collapsed")
            st.caption("成功")
        with col2:
            st.metric("🔄", execution_summary["running_count"], label_visibility="collapsed")
            st.caption("运行中")
        with col3:
            st.metric("❌", execution_summary["failure_count"], label_visibility="collapsed")
            st.caption("失败")

    # 显示下一步建议
    next_step_suggestion = workflow_manager.get_next_step_suggestion(current_step)
    if next_step_suggestion and current_step != "monitoring":
        st.markdown("---")
        can_proceed = workflow_manager.can_proceed_to_next_step(current_step)
        if can_proceed:
            st.info(f"💡 {next_step_suggestion}")
        else:
            st.warning(f"⏳ {next_step_suggestion} (需要完成当前步骤)")

    # 重置工作流按钮
    if st.button("🔄 重置工作流", use_container_width=True, help="清除所有工作流状态，重新开始"):
        workflow_manager.reset_workflow()
        st.rerun()

def reset_session_state():
    """重置会话状态"""
    for key in list(st.session_state.keys()):
        del st.session_state[key]