/* 全局字体和颜色 */
body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    color: #333;
}

/* 主标题 */
.st-emotion-cache-183lzff {
    font-size: 2.5rem;
    font-weight: 600;
    color: #1a1a1a;
}

/* 副标题 */
.st-emotion-cache-1H6k_10 {
    color: #555;
}

/* Streamlit Tabs 样式 */
.st-tabs [data-baseweb="tab-list"] {
    gap: 24px;
}

.st-tabs [data-baseweb="tab"] {
    height: 50px;
    white-space: pre-wrap;
    background-color: transparent;
    border-radius: 4px;
    margin: 0 !important;
    padding: 10px 16px;
    transition: background-color 0.3s, color 0.3s;
}

.st-tabs [data-baseweb="tab"]:hover {
    background-color: #f0f2f6;
    color: #0068c9;
}

.st-tabs [data-baseweb="tab"][aria-selected="true"] {
    background-color: #e6f1fc;
    color: #0068c9;
    font-weight: 600;
}

/* 按钮样式 */
.stButton>button {
    border-radius: 8px;
    padding: 10px 20px;
    font-weight: 500;
    transition: background-color 0.3s, border-color 0.3s;
}

.stButton>button:hover {
    background-color: #f0f2f6;
}

/* Expander 样式 */
.st-expander {
    border: 1px solid #e6e6e6;
    border-radius: 8px;
}

.st-expander header {
    font-weight: 600;
}