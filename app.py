import streamlit as st
import logging
import sys
from datetime import datetime
from src.ui.sidebar import display_sidebar
from src.ui.tabs import plan_query_tab, plan_approval_tab, execution_management_tab, monitoring_dashboard_tab, workflow_page
from src.ui.config_status import display_full_config_status
from src.ui.single_page_workflow import single_page_workflow
from src.constants import APP_TITLE, APP_ICON, APP_CAPTION, TAB_TITLES
from src.release_agent import ReleaseAgent

def setup_logging():
    """设置日志配置"""
    # 配置日志格式
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

    # 创建Streamlit日志处理器
    streamlit_handler = StreamlitLogHandler()
    streamlit_handler.setLevel(logging.INFO)

    # 配置根日志记录器
    logging.basicConfig(
        level=logging.INFO,
        format=log_format,
        handlers=[
            logging.StreamHandler(sys.stdout),  # 输出到控制台
            streamlit_handler  # 输出到Streamlit界面
        ]
    )

    # 设置特定模块的日志级别
    logging.getLogger('src.release_agent').setLevel(logging.INFO)
    logging.getLogger('src.agent_workflow').setLevel(logging.INFO)
    logging.getLogger('src.database').setLevel(logging.INFO)
    logging.getLogger('src.jenkins_client').setLevel(logging.INFO)

    logger = logging.getLogger(__name__)
    logger.info("🚀 应用启动 - 日志系统已初始化")
    return logger

class StreamlitLogHandler(logging.Handler):
    """自定义日志处理器，将日志存储到session state"""

    def emit(self, record):
        if 'log_messages' not in st.session_state:
            st.session_state.log_messages = []

        # 格式化日志消息
        log_entry = {
            'timestamp': datetime.fromtimestamp(record.created).strftime('%H:%M:%S'),
            'level': record.levelname,
            'message': record.getMessage(),
            'module': record.name
        }

        # 只保留最新的100条日志
        st.session_state.log_messages.append(log_entry)
        if len(st.session_state.log_messages) > 100:
            st.session_state.log_messages.pop(0)

def display_log_viewer():
    """显示日志查看器"""
    if 'log_messages' not in st.session_state:
        st.session_state.log_messages = []

    with st.expander("📋 实时日志", expanded=True):
        # 日志级别过滤
        col1, col2, col3 = st.columns([1, 1, 2])
        with col1:
            show_info = st.checkbox("INFO", value=True, key="log_info")
        with col2:
            show_error = st.checkbox("ERROR", value=True, key="log_error")
        with col3:
            if st.button("🗑️ 清空日志", key="clear_logs"):
                st.session_state.log_messages = []
                st.rerun()

        # 显示日志
        log_container = st.container()
        with log_container:
            if st.session_state.log_messages:
                for log_entry in reversed(st.session_state.log_messages[-20:]):  # 显示最新20条
                    level = log_entry['level']
                    if (level == 'INFO' and show_info) or (level == 'ERROR' and show_error) or level == 'WARNING':
                        # 根据日志级别设置颜色
                        if level == 'ERROR':
                            color = "#ff4444"
                        elif level == 'WARNING':
                            color = "#ffaa00"
                        else:
                            color = "#00aa00"

                        st.markdown(f"""
                        <div style="
                            padding: 5px 10px;
                            margin: 2px 0;
                            border-left: 3px solid {color};
                            background-color: #f8f9fa;
                            font-family: monospace;
                            font-size: 12px;
                        ">
                            <span style="color: #666;">[{log_entry['timestamp']}]</span>
                            <span style="color: {color}; font-weight: bold;">{level}</span>
                            <span style="color: #333;">{log_entry['message']}</span>
                        </div>
                        """, unsafe_allow_html=True)
            else:
                st.info("暂无日志消息")

def load_custom_css():
    """加载自定义CSS文件"""
    try:
        with open("src/ui/style.css", "r") as f:
            st.markdown(f'<style>{f.read()}</style>', unsafe_allow_html=True)
    except FileNotFoundError:
        st.warning("自定义样式文件 'src/ui/style.css' 未找到.")

def initialize_agent():
    """初始化Agent"""
    logger = logging.getLogger(__name__)

    if 'agent' not in st.session_state or st.session_state.agent is None:
        try:
            logger.info("🤖 开始初始化Agent...")
            with st.spinner("正在初始化Agent..."):
                agent = ReleaseAgent()
                st.session_state.agent = agent
            logger.info("✅ Agent初始化成功")
            return True
        except Exception as e:
            error_msg = f"Agent初始化失败: {str(e)}"
            logger.error(f"❌ {error_msg}")
            st.error(f"❌ {error_msg}")
            st.session_state.agent = None
            return False
    else:
        logger.info("🤖 Agent已存在，跳过初始化")
    return True

def initialize_session_state():
    """Initialize session state variables."""
    if 'current_view' not in st.session_state:
        st.session_state.current_view = 'main'

def main():
    """Main function to run the Streamlit app."""
    # 首先设置日志
    logger = setup_logging()

    st.set_page_config(
        page_title=APP_TITLE,
        page_icon=APP_ICON,
        layout="wide",
        initial_sidebar_state="expanded"
    )

    logger.info("📱 Streamlit页面配置完成")

    load_custom_css()
    logger.info("🎨 自定义CSS加载完成")

    initialize_session_state()
    logger.info("🔧 会话状态初始化完成")

    # 初始化Agent
    agent_initialized = initialize_agent()
    if not agent_initialized:
        logger.warning("⚠️ Agent未能正确初始化，部分功能可能不可用")
        st.warning("⚠️ Agent未能正确初始化，部分功能可能不可用")

    display_sidebar()

    st.title("智能发布助手")
    st.caption(APP_CAPTION)

    # 添加日志查看器
    if st.checkbox("📋 显示实时日志", key="show_logs"):
        display_log_viewer()

    # 添加单页面工作流和配置状态标签
    extended_tab_titles = ["🌊 工作流"] + TAB_TITLES + ["⚙️ 配置"]
    tab0, tab1, tab2, tab3, tab4, tab5, tab6 = st.tabs(extended_tab_titles)

    with tab0:
        single_page_workflow()
    with tab1:
        plan_query_tab()
    with tab2:
        plan_approval_tab()
    with tab3:
        execution_management_tab()
    with tab4:
        monitoring_dashboard_tab()
    with tab5:
        workflow_page()
    with tab6:
        display_full_config_status()

if __name__ == "__main__":
    main()